{"swagger": "2.0", "info": {"description": "Smart City System (SCS) Operator API for managing premises, alarms, incidents, guidance templates, and guards", "title": "SCS Operator API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/alarms": {"get": {"security": [{"BearerAuth": []}], "description": "Get all alarms with optional status filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["alarms"], "summary": "Get alarms", "parameters": [{"type": "string", "description": "Filter by alarm status (new, ignored, dispatched)", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Alarm"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/alarms/{id}": {"patch": {"security": [{"BearerAuth": []}], "description": "Update an existing alarm's status or other properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["alarms"], "summary": "Update alarm", "parameters": [{"type": "string", "description": "Alarm ID", "name": "id", "in": "path", "required": true}, {"description": "Alarm update data", "name": "alarm", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdateAlarmDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Alarm"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/guards": {"post": {"security": [{"BearerAuth": []}], "description": "Create a new guard user with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["guards"], "summary": "Create a new guard", "parameters": [{"description": "Guard creation data", "name": "guard", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateGuardDto"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.User"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/guidance-steps": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of all guidance steps", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["guidance-steps"], "summary": "Get all guidance steps", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.GuidanceStep"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new guidance step for a guidance template", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["guidance-steps"], "summary": "Create a new guidance step", "parameters": [{"description": "Guidance step creation data", "name": "step", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateGuidanceStepDto"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.GuidanceStep"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/guidance-steps/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a specific guidance step by its ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["guidance-steps"], "summary": "Get guidance step by ID", "parameters": [{"type": "string", "description": "Guidance Step ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.GuidanceStep"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/guidance-templates": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of all guidance templates", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["guidance-templates"], "summary": "Get all guidance templates", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.GuidanceTemplate"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new guidance template with steps", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["guidance-templates"], "summary": "Create a new guidance template", "parameters": [{"description": "Guidance template creation data", "name": "template", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateGuidanceTemplateDto"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.GuidanceTemplate"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/guidance-templates/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a specific guidance template by its ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["guidance-templates"], "summary": "Get guidance template by ID", "parameters": [{"type": "string", "description": "Guidance Template ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.GuidanceTemplate"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing guidance template and its steps", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["guidance-templates"], "summary": "Update guidance template", "parameters": [{"type": "string", "description": "Guidance Template ID", "name": "id", "in": "path", "required": true}, {"description": "Guidance template update data", "name": "template", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdateGuidanceTemplateDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.GuidanceTemplate"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/incidents": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of all incidents", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["incidents"], "summary": "Get incidents with pagination", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Number of items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/types.IncidentListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new incident with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["incidents"], "summary": "Create a new incident", "parameters": [{"description": "Incident creation data", "name": "incident", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateIncidentDto"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.Incident"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/incidents/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a specific incident by its ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["incidents"], "summary": "Get incident by ID", "parameters": [{"type": "string", "description": "Incident ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Incident"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}, "patch": {"security": [{"BearerAuth": []}], "description": "Update an existing incident's status or other properties", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["incidents"], "summary": "Update incident", "parameters": [{"type": "string", "description": "Incident ID", "name": "id", "in": "path", "required": true}, {"description": "Incident update data", "name": "incident", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdateIncidentDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Incident"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/incidents/{id}/assign-guidance": {"post": {"security": [{"BearerAuth": []}], "description": "Assign a guidance template to an incident with an assignee", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["incidents"], "summary": "Assign guidance to incident", "parameters": [{"type": "string", "description": "Incident ID", "name": "id", "in": "path", "required": true}, {"description": "Guidance assignment data", "name": "guidance", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.AssignGuidance"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.IncidentGuidance"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/incidents/{id}/complete": {"patch": {"security": [{"BearerAuth": []}], "description": "Mark an incident as resolved/completed", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["incidents"], "summary": "Complete incident", "parameters": [{"type": "string", "description": "Incident ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/incidents/{id}/guidance": {"get": {"security": [{"BearerAuth": []}], "description": "Get the guidance assigned to a specific incident", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["incidents"], "summary": "Get incident guidance", "parameters": [{"type": "string", "description": "Incident ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.IncidentGuidance"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/premises": {"get": {"description": "Get a paginated list of all premises", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["premises"], "summary": "Get premises with pagination", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Number of items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/types.PremiseListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}, "post": {"description": "Create a new premise with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["premises"], "summary": "Create a new premise", "parameters": [{"description": "Premise creation data", "name": "premise", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreatePremiseDto"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.Premise"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/premises/{id}": {"get": {"description": "Get a specific premise by its ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["premises"], "summary": "Get premise by ID", "parameters": [{"type": "string", "description": "Premise ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Premise"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}, "patch": {"description": "Update an existing premise with new information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["premises"], "summary": "Update premise", "parameters": [{"type": "string", "description": "Premise ID", "name": "id", "in": "path", "required": true}, {"description": "Premise update data", "name": "premise", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdatePremiseDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Premise"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/premises/{id}/assign-users": {"post": {"description": "Add or remove users from a premise", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["premises"], "summary": "Assign users to premise", "parameters": [{"type": "string", "description": "Premise ID", "name": "id", "in": "path", "required": true}, {"description": "User assignment data", "name": "users", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdatePremiseUserDto"}}], "responses": {"200": {"description": "success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}, "/premises/{id}/users": {"get": {"description": "Get all users assigned to a specific premise", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["premises"], "summary": "Get users assigned to premise", "parameters": [{"type": "string", "description": "Premise ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.User"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/errors.ErrorResponse"}}}}}}, "definitions": {"dto.AssignGuidance": {"type": "object", "required": ["assignee_id", "guidance_template_id"], "properties": {"assignee_id": {"type": "string"}, "guidance_template_id": {"type": "string"}}}, "dto.CreateGuardDto": {"type": "object", "required": ["email", "name", "password"], "properties": {"email": {"type": "string", "maxLength": 255}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "password": {"type": "string", "maxLength": 100, "minLength": 6}}}, "dto.CreateGuidanceStepDto": {"type": "object", "required": ["description", "guidance_template_id", "step_number", "title"], "properties": {"description": {"type": "string"}, "guidance_template_id": {"type": "string"}, "step_number": {"type": "integer"}, "title": {"type": "string"}}}, "dto.CreateGuidanceTemplateDto": {"type": "object", "required": ["description", "name"], "properties": {"category": {"type": "string"}, "description": {"type": "string"}, "name": {"type": "string"}, "steps": {"type": "array", "items": {"$ref": "#/definitions/dto.Step"}}}}, "dto.CreateIncidentDto": {"type": "object", "required": ["alarm_id", "assignee_id", "description", "guidance_template_id", "location", "name", "severity"], "properties": {"alarm_id": {"type": "string"}, "assignee_id": {"type": "string"}, "description": {"type": "string"}, "guidance_template_id": {"type": "string"}, "location": {"type": "string"}, "name": {"type": "string"}, "severity": {"type": "string"}}}, "dto.CreatePremiseDto": {"type": "object", "required": ["address", "name"], "properties": {"address": {"type": "string", "maxLength": 255, "minLength": 2}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "parent_premise_id": {"type": "string"}}}, "dto.Step": {"type": "object", "required": ["description", "step_number", "title"], "properties": {"description": {"type": "string"}, "id": {"type": "string"}, "step_number": {"type": "integer"}, "title": {"type": "string"}}}, "dto.UpdateAlarmDto": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}}, "dto.UpdateGuidanceTemplateDto": {"type": "object", "required": ["description", "name"], "properties": {"add_steps": {"type": "array", "items": {"$ref": "#/definitions/dto.Step"}}, "category": {"type": "string"}, "description": {"type": "string"}, "name": {"type": "string"}, "remove_steps": {"type": "array", "items": {"type": "string"}}, "update_steps": {"type": "array", "items": {"$ref": "#/definitions/dto.Step"}}}}, "dto.UpdateIncidentDto": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}}, "dto.UpdatePremiseDto": {"type": "object", "required": ["address", "name"], "properties": {"address": {"type": "string", "maxLength": 255, "minLength": 2}, "name": {"type": "string", "maxLength": 100, "minLength": 2}}}, "dto.UpdatePremiseUserDto": {"type": "object", "properties": {"added_users": {"type": "array", "items": {"type": "string"}}, "removed_users": {"type": "array", "items": {"type": "string"}}}}, "errors.ErrorDetail": {"type": "object", "properties": {"details": {}, "message": {"type": "string"}, "type": {"$ref": "#/definitions/errors.ErrorType"}}}, "errors.ErrorResponse": {"type": "object", "properties": {"error": {"$ref": "#/definitions/errors.ErrorDetail"}, "request_id": {"type": "string"}, "status": {"type": "integer"}, "timestamp": {"type": "string"}}}, "errors.ErrorType": {"type": "string", "enum": ["VALIDATION_ERROR", "NOT_FOUND", "UNAUTHORIZED", "FORBIDDEN", "BAD_REQUEST", "CONFLICT", "INTERNAL_ERROR", "DATABASE_ERROR", "EXTERNAL_SERVICE_ERROR", "TIMEOUT_ERROR"], "x-enum-varnames": ["ErrorTypeValidation", "ErrorTypeNotFound", "ErrorTypeUnauthorized", "ErrorTypeForbidden", "ErrorTypeBadRequest", "ErrorTypeConflict", "ErrorTypeInternal", "ErrorTypeDatabase", "ErrorTypeExternal", "ErrorTypeTimeout"]}, "models.Alarm": {"type": "object", "properties": {"created_at": {"type": "string"}, "description": {"type": "string"}, "device": {"type": "string"}, "id": {"type": "string"}, "premise": {"$ref": "#/definitions/models.Premise"}, "premise_id": {"type": "string"}, "severity": {"type": "string"}, "status": {"type": "string"}, "triggered_at": {"type": "string"}, "type": {"type": "string"}}}, "models.GuidanceStep": {"type": "object", "properties": {"created_at": {"type": "string"}, "description": {"type": "string"}, "guidance_template": {"$ref": "#/definitions/models.GuidanceTemplate"}, "guidance_template_id": {"type": "string"}, "id": {"type": "string"}, "step_number": {"type": "integer"}, "title": {"type": "string"}}}, "models.GuidanceTemplate": {"type": "object", "properties": {"category": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "guidance_steps": {"type": "array", "items": {"$ref": "#/definitions/models.GuidanceStep"}}, "id": {"type": "string"}, "name": {"type": "string"}}}, "models.Incident": {"type": "object", "properties": {"alarm": {"$ref": "#/definitions/models.Alarm"}, "alarm_id": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}, "incident_guidance": {"$ref": "#/definitions/models.IncidentGuidance"}, "incident_media": {"type": "array", "items": {"$ref": "#/definitions/models.IncidentMedia"}}, "location": {"type": "string"}, "name": {"type": "string"}, "severity": {"type": "string"}, "status": {"type": "string"}}}, "models.IncidentGuidance": {"type": "object", "properties": {"assignee": {"$ref": "#/definitions/models.User"}, "assignee_id": {"type": "string"}, "assigner": {"$ref": "#/definitions/models.User"}, "assigner_id": {"type": "string"}, "created_at": {"type": "string"}, "guidance_template": {"$ref": "#/definitions/models.GuidanceTemplate"}, "guidance_template_id": {"type": "string"}, "id": {"type": "string"}, "incident": {"$ref": "#/definitions/models.Incident"}, "incident_guidance_steps": {"type": "array", "items": {"$ref": "#/definitions/models.IncidentGuidanceStep"}}, "incident_id": {"type": "string"}}}, "models.IncidentGuidanceStep": {"type": "object", "properties": {"completed_at": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}, "incident_guidance": {"$ref": "#/definitions/models.IncidentGuidance"}, "incident_guidance_id": {"type": "string"}, "is_completed": {"type": "boolean"}, "step_number": {"type": "integer"}, "title": {"type": "string"}}}, "models.IncidentMedia": {"type": "object", "properties": {"created_at": {"type": "string"}, "file_name": {"type": "string"}, "file_size": {"type": "integer"}, "file_type": {"type": "string"}, "file_url": {"type": "string"}, "id": {"type": "string"}, "incident": {"$ref": "#/definitions/models.Incident"}, "incident_id": {"type": "string"}, "media_type": {"type": "string"}}}, "models.Premise": {"type": "object", "properties": {"address": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "parent_premise": {"$ref": "#/definitions/models.Premise"}, "parent_premise_id": {"type": "string"}}}, "models.User": {"type": "object", "properties": {"created_at": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}}}, "types.IncidentListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.Incident"}}, "pagination": {"$ref": "#/definitions/types.Pagination"}}}, "types.Pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "page": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "types.PremiseListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.Premise"}}, "pagination": {"$ref": "#/definitions/types.Pagination"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}
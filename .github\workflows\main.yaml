# This is a basic workflow to help you get started with Actions

name: CI/CD

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2.4.2
      
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v5
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: erik1303/scss:scs-operator

  # deploy:
  #   needs: [build]
  #   if: github.ref == 'refs/heads/main'
  #   runs-on: ubuntu-latest
  #   steps:
  #       - name: executing remote ssh commands using password
  #         if: github.event_name != 'pull_request'
  #         uses: appleboy/ssh-action@master
  #         with:
  #           host: ${{ secrets.HOST }}
  #           username: ${{ secrets.USERNAME }}
  #           password: ${{ secrets.PASSWORD }}
  #           port: ${{ secrets.PORT }}
  #           script: cd /home/<USER>/ssre && sh general-deploy.sh crm-dev-ec crm